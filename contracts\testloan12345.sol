// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import {ISwapRouter} from "@uniswap/v3-periphery/contracts/interfaces/ISwapRouter.sol";
import {TransferHelper} from "@uniswap/v3-periphery/contracts/libraries/TransferHelper.sol";

interface IVault {
    function flashLoan(
        address recipient,
        IERC20[] calldata tokens,
        uint256[] calldata amounts,
        bytes calldata userData
    ) external;
}

interface IFlashLoanRecipient {
    function receiveFlashLoan(
        IERC20[] calldata tokens,
        uint256[] calldata amounts,
        uint256[] calldata feeAmounts,
        bytes calldata userData
    ) external;
}

contract testloan123 is IFlashLoanRecipient {
    using SafeERC20 for IERC20;

    IVault public immutable vault;
    IERC20 public immutable token; // USDC

    // --- Uniswap V3 相关常量 ---
    ISwapRouter public constant SWAP_ROUTER =
        ISwapRouter(0xE592427A0AEce92De3Edee1F18E0157C05861564);
    address public constant USDC = 0xA0b86a33E6441B171DFE8ab681D8F0Bc3C3cEaE5;
    address public constant POL = 0x0d500B1d8E8eF31E21C99d1Db9A6444d3ADf1270;
    uint24 public constant POOL_FEE = 500;

    constructor(address _vault, address _token) {
        vault = IVault(_vault);
        token = IERC20(_token);
    }

    // 任何人都能触发一次“借完就还”的闪电贷
    function flash(uint256 amount) external {
        IERC20[] memory tokens = new IERC20[](1);
        tokens[0] = token;

        uint256[] memory amounts = new uint256[](1);
        amounts[0] = amount;

        vault.flashLoan(address(this), tokens, amounts, "");
    }

    // ----------------------------------
    // 闪电贷回调
    // ----------------------------------
    function receiveFlashLoan(
        IERC20[] calldata tokens,
        uint256[] calldata amounts,
        uint256[] calldata feeAmounts,
        bytes calldata
    ) external override {
        require(msg.sender == address(vault), "!vault");
        require(tokens.length == 1 && tokens[0] == token, "Token mismatch");

        uint256 loanAmount = amounts[0];

        // ---- 1. 把 USDC 换成 POL ----
        // 先 approve 给 router
        TransferHelper.safeApprove(USDC, address(SWAP_ROUTER), loanAmount);

        ISwapRouter.ExactInputSingleParams memory params1 = ISwapRouter
            .ExactInputSingleParams({
                tokenIn: USDC,
                tokenOut: POL,
                fee: POOL_FEE,
                recipient: address(this),
                deadline: block.timestamp,
                amountIn: loanAmount,
                amountOutMinimum: 0,
                sqrtPriceLimitX96: 0
            });
        uint256 polAmount = SWAP_ROUTER.exactInputSingle(params1);

        // ---- 2. 把 POL 换回 USDC ----
        TransferHelper.safeApprove(POL, address(SWAP_ROUTER), polAmount);

        ISwapRouter.ExactInputSingleParams memory params2 = ISwapRouter
            .ExactInputSingleParams({
                tokenIn: POL,
                tokenOut: USDC,
                fee: POOL_FEE,
                recipient: address(this),
                deadline: block.timestamp,
                amountIn: polAmount,
                amountOutMinimum: 0,
                sqrtPriceLimitX96: 0
            });
        SWAP_ROUTER.exactInputSingle(params2);

        // ---- 3. 归还闪电贷本金 + 手续费 ----
        uint256 repay = loanAmount + feeAmounts[0];
        uint256 balance = tokens[0].balanceOf(address(this));

        if (balance < repay) {
            // 余额不足，直接 revert 并给出具体数字
            revert(
                string(
                    abi.encodePacked(
                        "Insufficient balance to repay flash loan. ",
                        "Required: ",
                        uintToStr(repay),
                        ", Available: ",
                        uintToStr(balance)
                    )
                )
            );
        }

        tokens[0].safeTransfer(address(vault), repay);
    }

    // ---- 工具：uint256 → string ----
    function uintToStr(uint256 value) internal pure returns (string memory) {
        if (value == 0) return "0";
        uint256 temp = value;
        uint256 digits;
        while (temp != 0) {
            digits++;
            temp /= 10;
        }
        bytes memory buffer = new bytes(digits);
        while (value != 0) {
            digits -= 1;
            buffer[digits] = bytes1(uint8(48 + uint256(value % 10)));
            value /= 10;
        }
        return string(buffer);
    }
}
